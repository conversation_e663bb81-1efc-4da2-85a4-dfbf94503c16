{"name": "moonbot-nest", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start:server": "nest start server", "start:server:dev": "nest start --watch server", "start:server:debug": "nest start --debug --watch server", "start:server:prod": "node dist/apps/moonbot-nest/server", "start:bot": "nest start bot", "start:bot:dev": "nest start --watch bot", "start:bot:debug": "nest start --debug --watch bot", "start:bot:prod": "node dist/apps/moonbot-nest/bot", "start:candles": "nest start candles", "start:candles:dev": "nest start --watch candles", "start:candles:debug": "nest start --debug --watch candles", "start:candles:prod": "node dist/apps/moonbot-nest/candles", "start:orders": "nest start orders", "start:orders:dev": "nest start --watch orders", "start:orders:debug": "nest start --debug --watch orders", "start:orders:prod": "node dist/apps/moonbot-nest/orders", "start:pump": "nest start pump", "start:pump:dev": "nest start --watch pump", "start:pump:debug": "nest start --debug --watch pump", "start:pump:prod": "node dist/apps/moonbot-nest/pump", "start:reports": "nest start reports", "start:reports:dev": "nest start --watch reports", "start:reports:debug": "nest start --debug --watch reports", "start:reports:prod": "node dist/apps/moonbot-nest/reports", "start:tester": "nest start tester", "start:tester:dev": "nest start --watch tester", "start:tester:debug": "nest start --debug --watch tester", "start:tester:prod": "node dist/apps/moonbot-nest/tester", "start:indicators": "nest start indicators", "start:indicators:dev": "nest start --watch indicators", "start:indicators:debug": "nest start --debug --watch indicators", "start:indicators:prod": "node dist/apps/moonbot-nest/indicators", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/moonbot-nest/test/jest-e2e.json", "gen-types": "npx gql2ts schema.graphql -o types/schema.types.d.ts && ts-node generate-typings.ts"}, "dependencies": {"@apollo/server": "^4.11.3", "@as-integrations/fastify": "^2.1.1", "@fastify/secure-session": "^8.1.1", "@formkit/tempo": "^0.1.2", "@nestjs/apollo": "^13.0.2", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^11.0.9", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.9", "@nestjs/graphql": "^13.0.2", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/mongoose": "^11.0.1", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^11.0.9", "@nestjs/platform-fastify": "^11.0.9", "@nestjs/schedule": "^5.0.1", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "axios": "^1.7.9", "bcrypt": "^5.1.1", "binance-api-node": "^0.12.9", "bull": "^4.16.5", "bybit-api": "^3.10.32", "cache-manager": "^6.4.0", "cache-manager-redis-store": "^3.0.1", "cheerio": "1.0.0-rc.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "connect-redis": "^7.1.1", "convnetjs": "^0.3.0", "cookie-parser": "^1.4.7", "dotenv": "^16.4.7", "express-session": "^1.18.1", "gateio-api": "^1.1.4", "graphql": "^16.10.0", "graphql-redis-subscriptions": "^2.7.0", "graphql-subscriptions": "^3.0.0", "ioredis": "^5.5.0", "mathjs": "^14.2.1", "mongoose": "8.10.0", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "ramda": "^0.30.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "technicalindicators": "^3.1.0", "ts-morph": "^25.0.1", "uuid": "^11.0.5"}, "devDependencies": {"@nestjs/cli": "^11.0.2", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.9", "@types/bcrypt": "^5.0.2", "@types/bluebird": "^3.5.42", "@types/express": "^5.0.0", "@types/express-session": "^1.18.1", "@types/node": "^22.13.1", "@types/node-schedule": "^2.1.7", "@types/ramda": "^0.30.2", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "prettier": "^3.5.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}, "pnpm": {"onlyBuiltDependencies": ["bcrypt"]}}